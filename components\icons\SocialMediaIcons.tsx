
import React from 'react';

interface SVGProps extends React.SVGProps<SVGSVGElement> {}

export const FacebookIcon: React.FC<SVGProps> = (props) => (
  <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
    <path d="M18 2h-3a5 5 0 00-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 011-1h3z" />
  </svg>
);

export const InstagramIcon: React.FC<SVGProps> = (props) => (
  <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
    <path d="M12 2C8.13401 2 5 5.13401 5 9C5 12.866 8.13401 16 12 16C15.866 16 19 12.866 19 9C19 5.13401 15.866 2 12 2ZM12 14C9.23858 14 7 11.7614 7 9C7 6.23858 9.23858 4 12 4C14.7614 4 17 6.23858 17 9C17 11.7614 14.7614 14 12 14ZM16.5 6.75C16.5 7.16421 16.1642 7.5 15.75 7.5C15.3358 7.5 15 7.16421 15 6.75C15 6.33579 15.3358 6 15.75 6C16.1642 6 16.5 6.33579 16.5 6.75Z" />
  </svg>
);

export const TwitterIcon: React.FC<SVGProps> = (props) => (
  <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
    <path d="M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z" />
  </svg>
);

export const LinkedInIcon: React.FC<SVGProps> = (props) => (
  <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
    <path d="M16 8a6 6 0 016 6v7h-4v-7a2 2 0 00-2-2 2 2 0 00-2 2v7h-4v-7a6 6 0 016-6zM2 9h4v12H2zM4 6a2 2 0 100-4 2 2 0 000 4z" />
  </svg>
);

export const YouTubeIcon: React.FC<SVGProps> = (props) => (
  <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
    <path d="M21.582 6.185A2.783 2.783 0 0019.53 5.4C17.957 5 12 5 12 5s-5.957 0-7.53.4c-1.397.278-2.052.703-2.052 2.785C2.418 8.043 2 12 2 12s.418 3.957.418 5.815c0 2.082.655 2.507 2.052 2.785C6.043 21 12 21 12 21s5.957 0 7.53-.4c1.397-.278 2.052-.703 2.052-2.785C21.582 15.957 22 12 22 12s-.418-3.957-.418-5.815zM9.75 15.5V8.5l6.5 3.5-6.5 3.5z" />
  </svg>
);

export const WhatsAppIcon: React.FC<SVGProps> = (props) => (
  <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
    <path d="M12.04 2C6.58 2 2.13 6.45 2.13 11.91C2.13 13.66 2.59 15.35 3.43 16.84L2 22l5.32-1.38C8.69 21.46 10.33 21.9 12.04 21.9C17.5 21.9 21.95 17.45 21.95 11.99C21.95 6.53 17.5 2 12.04 2ZM12.04 20.14C10.56 20.14 9.14 19.74 7.94 19.03L7.54 18.79L4.85 19.45L5.54 16.85L5.27 16.42C4.52 15.15 4.09 13.68 4.09 12C4.09 7.58 7.66 4.04 12.04 4.04C16.42 4.04 20 7.58 20 12C20 16.42 16.42 20.14 12.04 20.14ZM16.51 14.63C16.27 14.52 15.03 13.91 14.8 13.82C14.58 13.73 14.41 13.69 14.25 13.93C14.09 14.17 13.62 14.72 13.48 14.89C13.34 15.05 13.2 15.07 12.96 14.96C12.72 14.84 11.89 14.56 10.89 13.68C10.13 13.01 9.63 12.18 9.49 11.94C9.35 11.71 9.46 11.59 9.58 11.47C9.69 11.36 9.83 11.17 9.97 11.01C10.11 10.86 10.16 10.74 10.24 10.56C10.32 10.38 10.28 10.22 10.22 10.1C10.16 9.99 9.71 8.84 9.54 8.45C9.37 8.06 9.2 8.11 9.07 8.1L8.76 8.1C8.6 8.1 8.35 8.17 8.14 8.41C7.94 8.65 7.42 9.12 7.42 10.22C7.42 11.32 8.16 12.35 8.3 12.51C8.44 12.67 9.74 14.68 11.69 15.45C12.44 15.76 12.94 15.93 13.33 16.06C13.88 16.23 14.36 16.19 14.71 16.12C15.1 16.03 16.06 15.47 16.27 15.23C16.48 14.99 16.48 14.74 16.51 14.63Z"/>
  </svg>
);

export const GlobeIcon: React.FC<SVGProps> = (props) => (
  <svg fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M3.53 9h16.94M3.53 15h16.94M12 2.53v18.94M9 3.53a15.1 15.1 0 016 0M9 20.47a15.1 15.1 0 006 0" />
  </svg>
);

export const BrochureIcon: React.FC<SVGProps> = (props) => (
  <svg fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
);

export const FeedbackIcon: React.FC<SVGProps> = (props) => (
  <svg fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-4 4v-4z" />
  </svg>
);

export const HospitalLogoIcon: React.FC<SVGProps> = (props) => (
    // Simplified Lotus/Devotion like symbol
  <svg viewBox="0 0 60 60" fill="currentColor" {...props}>
    <path d="M30 6 Q 35 12 40 6 Q 45 15 30 25 Q 15 15 20 6 Q 25 12 30 6 Z" className="text-orange-500"/>
    <path d="M30 20 Q 40 25 50 20 Q 55 35 30 45 Q 5 35 10 20 Q 20 25 30 20 Z" className="text-red-500 opacity-80"/>
    <path d="M30 36 Q 38 40 46 36 Q 50 48 30 55 Q 10 48 14 36 Q 22 40 30 36 Z" className="text-blue-500 opacity-70"/>
    <circle cx="30" cy="30" r="5" className="text-yellow-400"/>
    <text x="10" y="52" fontFamily="Arial, sans-serif" fontSize="6" fill="navy" className="font-semibold">SERVING IN DEVOTION</text>
  </svg>
);
