
import React from 'react';
import { PatientData } from '../types';
import { FacebookIcon, InstagramIcon, TwitterIcon, LinkedInIcon, YouTubeIcon, WhatsAppIcon, GlobeIcon, BrochureIcon, FeedbackIcon, HospitalLogoIcon } from './icons/SocialMediaIcons';

interface PatientIdCardProps {
  patientData: PatientData;
}

const Barcode: React.FC<{ value: string }> = ({ value }) => {
  // This is a visual representation, not a scannable barcode
  return (
    <div className="w-full h-12 flex items-end" aria-label={`Barcode value ${value}`}>
      {Array.from({ length: 40 }).map((_, index) => (
        <div
          key={index}
          className="bg-black"
          style={{
            width: `${Math.random() * 2 + 0.5}px`, // Thinner bars: 0.5 to 2.5px
            height: `${Math.random() * 50 + 50}%`, // Varying heights for visual effect
            marginRight: '0.5px', // Tiny space between bars
          }}
        />
      ))}
    </div>
  );
};

const DecorativeLine: React.FC = () => (
  <div className="relative my-4 h-px bg-red-300">
    <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-red-400 transform rotate-45"></div>
  </div>
);

// Simplified decorative corner element
const CornerAccent: React.FC<{ position: string }> = ({ position }) => {
  let classes = "absolute w-8 h-8 border-yellow-600 opacity-50";
  if (position.includes('top')) classes += " top-0";
  if (position.includes('bottom')) classes += " bottom-0";
  if (position.includes('left')) classes += " left-0 border-t border-l";
  if (position.includes('right')) classes += " right-0 border-t border-r";
  
  if (position === 'top-left') return <div className="absolute -top-1 -left-1 w-6 h-6 border-l-2 border-t-2 border-amber-600"></div>;
  if (position === 'top-right') return <div className="absolute -top-1 -right-1 w-6 h-6 border-r-2 border-t-2 border-amber-600"></div>;
  if (position === 'bottom-left') return <div className="absolute -bottom-1 -left-1 w-6 h-6 border-l-2 border-b-2 border-amber-600"></div>;
  if (position === 'bottom-right') return <div className="absolute -bottom-1 -right-1 w-6 h-6 border-r-2 border-b-2 border-amber-600"></div>;
  return null;
};


const PatientIdCard: React.FC<PatientIdCardProps> = ({ patientData }) => {
  const {
    hospitalName,
    hospitalSubtitle,
    photoUrl,
    mrnBhId,
    name,
    age,
    registeredMobileNumber,
    barcodeValue,
    websiteUrl,
    appointmentPhoneNumber,
    whatsappBhaktiNumber,
  } = patientData;

  return (
    <div className="w-[380px] bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50 p-3 shadow-2xl rounded-xl border-2 border-orange-400 relative">
      <div className="border border-orange-300 rounded-lg p-3 bg-white/50 relative">
        {/* Decorative Corners for inner card */}
        <CornerAccent position="top-left" />
        <CornerAccent position="top-right" />
       

        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <HospitalLogoIcon className="h-14 w-14 text-blue-700" />
            <div>
              <h1 className="text-xl font-bold text-blue-800 tracking-tight">{hospitalName}</h1>
              <p className="text-xs text-blue-700 font-medium">{hospitalSubtitle}</p>
            </div>
          </div>
        </div>
        <div className="text-center mb-3">
            <span className="text-sm font-semibold text-blue-600 bg-blue-100 px-4 py-1 rounded-md shadow-sm border border-blue-200">MRN/BH Card</span>
        </div>

        {/* Patient Info & Photo & Barcode */}
        <div className="bg-white/70 p-3 rounded-lg shadow border border-orange-200 mb-3">
          <div className="flex">
            <div className="w-1/3 mr-3 flex flex-col items-center">
              <div className="w-24 h-32 rounded-lg border-2 border-orange-400 overflow-hidden shadow-md">
                <img src={photoUrl} alt="Patient" className="w-full h-full object-cover" />
              </div>
            </div>
            <div className="w-2/3 text-xs space-y-1">
              <p><strong className="text-red-700 font-semibold">MRN/BH ID No.:</strong> <span className="text-gray-800">{mrnBhId}</span></p>
              <p><strong className="text-red-700 font-semibold">Name:</strong> <span className="text-gray-800">{name}</span></p>
              <p><strong className="text-red-700 font-semibold">Age:</strong> <span className="text-gray-800">{age}</span></p>
              <p><strong className="text-red-700 font-semibold">Registered Mobile Number:</strong> <span className="text-gray-800">{registeredMobileNumber}</span></p>
            </div>
          </div>
          <div className="mt-2 px-2">
            <Barcode value={barcodeValue} />
            <p className="text-center text-xs text-gray-600 mt-0.5 tracking-wider">{barcodeValue}</p>
          </div>
        </div>
        

        {/* Instructions */}
        <div className="bg-white/70 p-3 rounded-lg shadow border border-orange-200 mb-3 relative">
          <CornerAccent position="top-left" />
          <CornerAccent position="top-right" />
          <CornerAccent position="bottom-left" />
          <CornerAccent position="bottom-right" />
          <h2 className="text-sm font-bold text-blue-700 mb-1.5 underline decoration-orange-400 decoration-2 underline-offset-2">Instructions for Patients</h2>
          <ul className="list-disc list-inside text-xs text-gray-700 space-y-0.5">
            <li>Please present this card at the billing desk for prompt service.</li>
            <li>Please use the MRN/BH number while booking the appointment.</li>
            <li>For appointment call on <strong className="text-red-700">{appointmentPhoneNumber}</strong></li>
          </ul>
        </div>
        
        <DecorativeLine />

        {/* Patient Care Information Button */}
        <div className="text-center my-1">
          <span className="text-base font-semibold text-indigo-700 bg-indigo-100 px-6 py-2 rounded-lg shadow-md border border-indigo-200">Patient Care Information</span>
        </div>

        {/* Links Section */}
        <div className="bg-white/70 p-3 rounded-lg shadow border border-orange-200 mb-3 relative">
           <CornerAccent position="top-left" />
           <CornerAccent position="top-right" />
           <CornerAccent position="bottom-left" />
           <CornerAccent position="bottom-right" />
          <div className="flex items-center mb-2">
            <GlobeIcon className="w-8 h-8 text-red-600 mr-2" />
            <div>
              <a href={`http://${websiteUrl}`} target="_blank" rel="noopener noreferrer" className="text-xs text-red-700 hover:underline font-semibold">
                Check out our Website: {websiteUrl}
              </a>
              <p className="text-xs text-red-700 hover:underline font-semibold">Book Doctor Appointment & Health Check Ups</p>
            </div>
          </div>
        </div>

        {/* Follow Us & Other Links */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="bg-white/70 p-2 rounded-lg shadow border border-orange-200 relative">
            <CornerAccent position="top-left" /> <CornerAccent position="bottom-right" />
            <h3 className="font-semibold text-red-700 mb-1 text-center underline decoration-orange-400">Follow Us On</h3>
            <div className="flex justify-around items-center">
              <a href="#" className="text-blue-700 hover:text-blue-900"><FacebookIcon className="w-5 h-5" /></a>
              <a href="#" className="text-pink-600 hover:text-pink-800"><InstagramIcon className="w-5 h-5" /></a>
              <a href="#" className="text-sky-500 hover:text-sky-700"><TwitterIcon className="w-5 h-5" /></a>
              <a href="#" className="text-blue-600 hover:text-blue-800"><LinkedInIcon className="w-5 h-5" /></a>
              <a href="#" className="text-red-600 hover:text-red-800"><YouTubeIcon className="w-5 h-5" /></a>
            </div>
          </div>

          <div className="bg-white/70 p-2 rounded-lg shadow border border-orange-200 flex flex-col items-center justify-center text-center relative">
             <CornerAccent position="top-right" /> <CornerAccent position="bottom-left" />
            <BrochureIcon className="w-6 h-6 text-red-600 mb-0.5" />
            <a href="#" className="text-red-700 hover:underline font-semibold leading-tight">Download Our Specialities Brochure</a>
          </div>

          <div className="bg-white/70 p-2 rounded-lg shadow border border-orange-200 flex flex-col items-center justify-center text-center relative">
             <CornerAccent position="top-left" /> <CornerAccent position="bottom-right" />
            <FeedbackIcon className="w-6 h-6 text-red-600 mb-0.5" />
            <a href="#" className="text-red-700 hover:underline font-semibold leading-tight">We Value Your Feedback</a>
          </div>

          <div className="bg-white/70 p-2 rounded-lg shadow border border-orange-200 flex flex-col items-center justify-center text-center relative">
            <CornerAccent position="top-right" /> <CornerAccent position="bottom-left" />
            <img src="https://picsum.photos/seed/nurse/40/40" alt="Bhakti Assistant" className="w-7 h-7 rounded-full mb-0.5" />
            <a href={`https://wa.me/${whatsappBhaktiNumber.replace(/\D/g, '')}`} target="_blank" rel="noopener noreferrer" className="text-red-700 hover:underline font-semibold leading-tight">
              Ask Bhakti on <WhatsAppIcon className="w-4 h-4 inline-block text-green-600" /> {whatsappBhaktiNumber}
            </a>
          </div>
        </div>
         <CornerAccent position="bottom-left" />
         <CornerAccent position="bottom-right" />
      </div>
    </div>
  );
};

export default PatientIdCard;
